# 可靠的 GitHub Pages 部署工作流
# 使用 peaceiris/actions-gh-pages 确保部署成功
# 作者: 9531lyj

name: 🚀 可靠部署到 GitHub Pages

# 触发条件
on:
  # 推送到 main 分支时触发
  push:
    branches: [ main ]
  # 手动触发
  workflow_dispatch:

# 设置权限
permissions:
  contents: read
  pages: write
  id-token: write

jobs:
  # 构建和部署作业
  deploy:
    name: 🔨 构建并部署
    runs-on: ubuntu-latest
    
    steps:
    # 步骤1: 检出代码
    - name: 📥 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    # 步骤2: 设置 Node.js 环境
    - name: 🟢 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    # 步骤3: 安装依赖
    - name: 📦 安装依赖
      run: |
        echo "🔍 检查项目信息..."
        echo "项目名称: $(cat package.json | grep '"name"' | head -1)"
        echo "项目版本: $(cat package.json | grep '"version"' | head -1)"
        echo "📦 开始安装依赖..."
        npm ci
        echo "✅ 依赖安装完成"
    
    # 步骤4: TypeScript 类型检查
    - name: 🔍 TypeScript 类型检查
      run: |
        echo "🔍 运行 TypeScript 类型检查..."
        npm run type-check
        echo "✅ 类型检查通过"
    
    # 步骤5: 构建项目
    - name: 🏗️ 构建项目
      env:
        NODE_ENV: production
      run: |
        echo "🏗️ 开始构建生产版本..."
        npm run build
        echo "✅ 构建完成"
        echo "📊 构建产物信息:"
        ls -la dist/
        du -sh dist/
        echo "📁 主要文件:"
        find dist/ -name "*.html" -o -name "*.js" -o -name "*.css" | head -10
    
    # 步骤6: 部署到 GitHub Pages
    - name: 🚀 部署到 GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
        publish_branch: gh-pages
        user_name: '9531lyj'
        user_email: '<EMAIL>'
        commit_message: |
          🚀 Deploy Space Battle Game v2.0
          
          ✨ 新功能:
          - 敌机击毁效果优化
          - UI布局改进
          - 控制说明移至右上角
          
          📅 部署时间: ${{ github.event.head_commit.timestamp }}
          📝 提交: ${{ github.sha }}
          👨‍💻 作者: 9531lyj
        force_orphan: true
        enable_jekyll: false
        cname: false
    
    # 步骤7: 部署成功通知
    - name: 🎉 部署成功通知
      run: |
        echo "🎉 太空战斗游戏部署成功！"
        echo "🌐 游戏地址: https://9531lyj.github.io/space-battle-game/"
        echo "📅 部署时间: $(date)"
        echo "🔗 GitHub 仓库: https://github.com/${{ github.repository }}"
        echo "👨‍💻 作者: 9531lyj"
        echo "📧 邮箱: <EMAIL>"
        echo ""
        echo "🎮 游戏特性:"
        echo "  ✨ 3D太空战斗体验"
        echo "  🎯 瞄准镜系统"
        echo "  ⚡ 技能系统"
        echo "  💥 敌机击毁效果"
        echo "  🚀 粒子特效"
        echo "  🎨 动态光照"
        echo ""
        echo "🎮 立即开始游戏: https://9531lyj.github.io/space-battle-game/"
        echo ""
        echo "📋 游戏控制:"
        echo "  WASD - 移动飞机"
        echo "  空格 - 发射炮弹"
        echo "  右键 - 瞄准模式"
        echo "  1234 - 使用技能"
        echo ""
        echo "🎯 新增击毁效果:"
        echo "  💥 普通炮弹 - 标准爆炸"
        echo "  ⚡ 激光武器 - 能量爆发"
        echo "  🚀 导弹武器 - 大爆炸冲击波"
