var e=Object.defineProperty,t=(t,s,i)=>((t,s,i)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[s]=i)(t,"symbol"!=typeof s?s+"":s,i);import{S as s,F as i,P as o,W as n,a,A as r,D as h,b as c,B as l,c as m,d,e as p,f as y,g as u,h as w,M as g,i as f,C as x,V as v,G as M,j as S,k as b,l as E,m as k,R as L,n as T,o as P,p as z}from"./three-DKRBiI99.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const s of e)if("childList"===s.type)for(const e of s.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();class F{constructor(e){t(this,"scene"),t(this,"camera"),t(this,"renderer"),t(this,"canvas"),this.canvas=e,this.initScene(),this.initCamera(),this.initRenderer(),this.initLighting(),this.createStarField()}initScene(){this.scene=new s,this.scene.fog=new i(0,1e3,1e4)}initCamera(){this.camera=new o(75,window.innerWidth/window.innerHeight,.1,1e4),this.camera.position.set(0,50,100)}initRenderer(){this.renderer=new n({canvas:this.canvas,antialias:!0,alpha:!0}),this.renderer.setSize(window.innerWidth,window.innerHeight),this.renderer.setPixelRatio(window.devicePixelRatio),this.renderer.shadowMap.enabled=!0,this.renderer.shadowMap.type=a,this.renderer.setClearColor(17,1)}initLighting(){const e=new r(4210816,.4);this.scene.add(e);const t=new h(16777215,1.2);t.position.set(200,200,100),t.castShadow=!0,t.shadow.mapSize.width=4096,t.shadow.mapSize.height=4096,t.shadow.camera.near=.5,t.shadow.camera.far=1e3,t.shadow.camera.left=-200,t.shadow.camera.right=200,t.shadow.camera.top=200,t.shadow.camera.bottom=-200,this.scene.add(t);const s=new c(35071,.8,500);s.position.set(-100,50,-50),this.scene.add(s);const i=new c(16729088,.6,300);i.position.set(100,-50,50),this.scene.add(i);const o=new c(65450,.5,200);o.name="dynamicLight",this.scene.add(o)}createStarField(){this.createStarLayer(15e3,1.5,16777215,"nearStars"),this.createStarLayer(8e3,1,11193599,"midStars"),this.createStarLayer(5e3,.5,8952319,"farStars"),this.createNebula()}createStarLayer(e,t,s,i){const o=new l,n=new Float32Array(3*e),a=new Float32Array(3*e);for(let c=0;c<3*e;c+=3){n[c]=3e4*(Math.random()-.5),n[c+1]=3e4*(Math.random()-.5),n[c+2]=3e4*(Math.random()-.5);const e=.3,t=(s>>16&255)/255,i=(s>>8&255)/255,o=(255&s)/255;a[c]=Math.min(1,t+(Math.random()-.5)*e),a[c+1]=Math.min(1,i+(Math.random()-.5)*e),a[c+2]=Math.min(1,o+(Math.random()-.5)*e)}o.setAttribute("position",new m(n,3)),o.setAttribute("color",new m(a,3));const r=new d({size:t,sizeAttenuation:!1,vertexColors:!0,transparent:!0,opacity:.8}),h=new p(o,r);h.name=i,this.scene.add(h)}createNebula(){const e=new l,t=new Float32Array(6e3),s=new Float32Array(6e3);for(let n=0;n<6e3;n+=3)t[n]=25e3*(Math.random()-.5),t[n+1]=25e3*(Math.random()-.5),t[n+2]=25e3*(Math.random()-.5),s[n]=.5+.5*Math.random(),s[n+1]=.2+.3*Math.random(),s[n+2]=.8+.2*Math.random();e.setAttribute("position",new m(t,3)),e.setAttribute("color",new m(s,3));const i=new d({size:8,sizeAttenuation:!0,vertexColors:!0,transparent:!0,opacity:.3,blending:y}),o=new p(e,i);o.name="nebula",this.scene.add(o)}onWindowResize(){this.camera.aspect=window.innerWidth/window.innerHeight,this.camera.updateProjectionMatrix(),this.renderer.setSize(window.innerWidth,window.innerHeight)}render(){this.renderer.render(this.scene,this.camera)}addToScene(e){this.scene.add(e)}removeFromScene(e){this.scene.remove(e)}}class j{constructor(e,s,i=!0){t(this,"mesh"),t(this,"position"),t(this,"velocity"),t(this,"damage"),t(this,"isPlayerProjectile"),t(this,"boundingBox"),this.position=e.clone(),this.velocity=s.clone(),this.damage=i?25:10,this.isPlayerProjectile=i,this.boundingBox=new u,this.createMesh()}createMesh(){const e=new w(.3,8,8),t=this.isPlayerProjectile?65280:16711680,s=new g({color:t});this.mesh=new f(e,s),this.mesh.position.copy(this.position);const i=new w(.6,8,8),o=new g({color:t,transparent:!0,opacity:.3}),n=new f(i,o);this.mesh.add(n),this.createTrail()}createTrail(){const e=new x(.1,.3,2,8),t=this.isPlayerProjectile?65280:16711680,s=new g({color:t,transparent:!0,opacity:.6}),i=new f(e,s);i.rotation.x=Math.PI/2,i.position.z=1,this.mesh.add(i)}update(e){this.position.add(this.velocity.clone().multiplyScalar(e)),this.mesh.position.copy(this.position),this.boundingBox.setFromObject(this.mesh),this.mesh.rotation.x+=.1,this.mesh.rotation.y+=.1}isOutOfBounds(){return Math.abs(this.position.x)>200||Math.abs(this.position.y)>200||Math.abs(this.position.z)>1e3}checkCollision(e){const t=(new u).setFromObject(e);return this.boundingBox.intersectsBox(t)}destroy(){this.createExplosion()}createExplosion(){for(let e=0;e<10;e++){const e=new w(.1,4,4),t=new g({color:this.isPlayerProjectile?65280:16711680,transparent:!0,opacity:.8}),s=new f(e,t);s.position.copy(this.position);const i=new v(2*(Math.random()-.5),2*(Math.random()-.5),2*(Math.random()-.5)).normalize();s.userData={velocity:i.multiplyScalar(10*Math.random()+5),life:1}}}getBoundingBox(){return this.boundingBox}}class C{constructor(){t(this,"mesh"),t(this,"position"),t(this,"velocity"),t(this,"health"),t(this,"maxHealth"),t(this,"speed"),t(this,"projectiles"),t(this,"lastShotTime"),t(this,"shotCooldown"),t(this,"weaponType"),t(this,"energy"),t(this,"maxEnergy"),t(this,"skills"),t(this,"animationTime"),this.position=new v(0,0,0),this.velocity=new v(0,0,0),this.health=100,this.maxHealth=100,this.speed=8,this.energy=100,this.maxEnergy=100,this.projectiles=[],this.lastShotTime=0,this.shotCooldown=150,this.weaponType="normal",this.animationTime=0,this.initSkills(),this.createMesh()}createMesh(){this.mesh=new M;const e=new x(.8,1.5,10,12),t=new S({color:2201331,shininess:100,specular:5227511}),s=new f(e,t);s.rotation.x=Math.PI/2,s.castShadow=!0,s.receiveShadow=!0,this.mesh.add(s);const i=new w(1.2,12,8),o=new S({color:1668818,transparent:!0,opacity:.8,shininess:150}),n=new f(i,o);n.position.set(0,.5,2),n.scale.set(1,.6,1.2),n.castShadow=!0,this.mesh.add(n);const a=new b(12,.3,3),r=new S({color:1402304,shininess:80}),h=new f(a,r);h.position.set(0,0,-1),h.castShadow=!0,this.mesh.add(h);const c=new b(6,.2,1.5),l=new f(c,r);l.position.set(0,0,-3),l.castShadow=!0,this.mesh.add(l);const m=new b(1,.3,.8),d=new S({color:4342338}),p=new f(m,d);p.position.set(-4,-.2,0),this.mesh.add(p);const y=new f(m,d);y.position.set(4,-.2,0),this.mesh.add(y);const u=new x(.6,.8,2,8),g=new S({color:3622735,shininess:120}),v=new f(u,g);v.position.set(-2.5,0,-4.5),v.rotation.x=Math.PI/2,v.castShadow=!0,this.mesh.add(v);const E=new f(u,g);E.position.set(2.5,0,-4.5),E.rotation.x=Math.PI/2,E.castShadow=!0,this.mesh.add(E),this.createEngineFlames(),this.createLights(),this.mesh.position.copy(this.position)}createEngineFlames(){const e=new E(.4,3,8),t=new g({color:16729088,transparent:!0,opacity:.8}),s=new f(e,t);s.position.set(-2.5,0,-7),s.rotation.x=Math.PI/2,s.name="leftFlame",this.mesh.add(s);const i=new f(e,t);i.position.set(2.5,0,-7),i.rotation.x=Math.PI/2,i.name="rightFlame",this.mesh.add(i);const o=new E(.2,2,6),n=new g({color:43775,transparent:!0,opacity:.9}),a=new f(o,n);a.position.set(-2.5,0,-6.5),a.rotation.x=Math.PI/2,a.name="leftInnerFlame",this.mesh.add(a);const r=new f(o,n);r.position.set(2.5,0,-6.5),r.rotation.x=Math.PI/2,r.name="rightInnerFlame",this.mesh.add(r)}createLights(){const e=new w(.1,6,6),t=new g({color:16711680}),s=new f(e,t);s.position.set(-6,0,-1),this.mesh.add(s);const i=new g({color:65280}),o=new f(e,i);o.position.set(6,0,-1),this.mesh.add(o);const n=new w(.15,8,8),a=new g({color:16777215}),r=new f(n,a);r.position.set(0,1,2),r.name="strobeLight",this.mesh.add(r)}initSkills(){this.skills={rapidFire:{name:"快速射击",cooldown:5e3,lastUsed:0,energy:30,maxEnergy:30},laserBeam:{name:"激光束",cooldown:8e3,lastUsed:0,energy:50,maxEnergy:50},missile:{name:"导弹",cooldown:1e4,lastUsed:0,energy:40,maxEnergy:40},shield:{name:"护盾",cooldown:15e3,lastUsed:0,energy:60,maxEnergy:60}}}update(e){this.animationTime+=e,this.position.add(this.velocity.clone().multiplyScalar(e)),this.mesh.position.copy(this.position),this.position.x=Math.max(-120,Math.min(120,this.position.x)),this.position.y=Math.max(-60,Math.min(60,this.position.y)),this.position.z=Math.max(-150,Math.min(150,this.position.z)),this.mesh.rotation.z=.1*-this.velocity.x,this.mesh.rotation.x=.05*this.velocity.y,this.updateEngineFlames(),this.updateStrobeLight(),this.projectiles.forEach(((t,s)=>{t.update(e),t.position.z<-1e3&&this.projectiles.splice(s,1)})),this.energy=Math.min(this.maxEnergy,this.energy+10*e),this.velocity.multiplyScalar(.92)}updateEngineFlames(){const e=this.mesh.getObjectByName("leftFlame"),t=this.mesh.getObjectByName("rightFlame"),s=this.mesh.getObjectByName("leftInnerFlame"),i=this.mesh.getObjectByName("rightInnerFlame");if(e&&t&&s&&i){const o=1+.1*this.velocity.length();e.scale.y=o+.2*Math.sin(10*this.animationTime),t.scale.y=o+.2*Math.sin(10*this.animationTime+1),s.scale.y=o+.3*Math.sin(15*this.animationTime),i.scale.y=o+.3*Math.sin(15*this.animationTime+.5)}}updateStrobeLight(){const e=this.mesh.getObjectByName("strobeLight");if(e){const t=e.material,s=.5+.3*Math.sin(8*this.animationTime);t.opacity=s}}moveLeft(){this.velocity.x=Math.max(this.velocity.x-.3*this.speed,1.5*-this.speed)}moveRight(){this.velocity.x=Math.min(this.velocity.x+.3*this.speed,1.5*this.speed)}moveUp(){this.velocity.y=Math.min(this.velocity.y+.3*this.speed,1.2*this.speed)}moveDown(){this.velocity.y=Math.max(this.velocity.y-.3*this.speed,1.2*-this.speed)}moveForward(){this.velocity.z=Math.max(this.velocity.z-.3*this.speed,1.5*-this.speed)}moveBackward(){this.velocity.z=Math.min(this.velocity.z+.3*this.speed,1.2*this.speed)}shoot(e){const t=Date.now();if(t-this.lastShotTime<this.shotCooldown)return!1;this.lastShotTime=t;let s=new v(0,0,-50);if(e&&(s=e.clone().multiplyScalar(50)),"rapid"===this.weaponType)for(let i=0;i<4;i++){const e=1.5*(i-1.5),t=new j(new v(this.position.x+e,this.position.y,this.position.z),s.clone(),!0);this.projectiles.push(t)}else{const e=new j(new v(this.position.x-2,this.position.y,this.position.z),s.clone(),!0),t=new j(new v(this.position.x+2,this.position.y,this.position.z),s.clone(),!0);this.projectiles.push(e,t)}return!0}takeDamage(e){this.health=Math.max(0,this.health-e)}heal(e){this.health=Math.min(this.maxHealth,this.health+e)}isAlive(){return this.health>0}getProjectiles(){return this.projectiles}removeProjectile(e){const t=this.projectiles.indexOf(e);t>-1&&this.projectiles.splice(t,1)}useSkill(e){const t=this.skills[e];if(!t)return!1;const s=Date.now();if(s-t.lastUsed<t.cooldown||this.energy<t.energy)return!1;switch(t.lastUsed=s,this.energy-=t.energy,e){case"rapidFire":return this.activateRapidFire();case"laserBeam":return this.activateLaserBeam();case"missile":return this.activateMissile();case"shield":return this.activateShield();default:return!1}}activateRapidFire(){return this.weaponType="rapid",setTimeout((()=>{this.weaponType="normal"}),3e3),!0}activateLaserBeam(){for(let e=0;e<5;e++)setTimeout((()=>{const e=new j(new v(this.position.x,this.position.y,this.position.z),new v(0,0,-80),!0);e.damage=50,this.projectiles.push(e)}),100*e);return!0}activateMissile(){const e=new j(new v(this.position.x,this.position.y,this.position.z),new v(0,0,-60),!0);return e.damage=100,this.projectiles.push(e),!0}activateShield(){return this.health=Math.min(this.maxHealth,this.health+50),!0}getSkillCooldown(e){const t=this.skills[e];if(!t)return 0;const s=Date.now()-t.lastUsed;return Math.max(0,t.cooldown-s)}canUseSkill(e){const t=this.skills[e];return!!t&&(0===this.getSkillCooldown(e)&&this.energy>=t.energy)}}class A{constructor(e,s="straight"){t(this,"mesh"),t(this,"position"),t(this,"velocity"),t(this,"health"),t(this,"maxHealth"),t(this,"speed"),t(this,"projectiles"),t(this,"boundingBox"),t(this,"lastShotTime"),t(this,"shotCooldown"),t(this,"movementPattern"),t(this,"movementTime"),this.position=e.clone(),this.velocity=new v(0,0,2),this.health=50,this.maxHealth=50,this.speed=3,this.projectiles=[],this.boundingBox=new u,this.lastShotTime=0,this.shotCooldown=1e3,this.movementPattern=s,this.movementTime=0,this.createMesh()}createMesh(){this.mesh=new M;const e=new x(1.2,.8,8,8),t=new S({color:15022389,shininess:80,specular:16733986}),s=new f(e,t);s.rotation.x=-Math.PI/2,s.castShadow=!0,s.receiveShadow=!0,this.mesh.add(s);const i=new b(2.5,1,6),o=new S({color:12986408,shininess:60}),n=new f(i,o);n.position.set(0,.3,0),n.castShadow=!0,this.mesh.add(n);const a=new b(8,.4,2.5),r=new S({color:12000284,shininess:70}),h=new f(a,r);h.position.set(0,0,1),h.castShadow=!0,this.mesh.add(h);const c=new b(.8,.8,1.5),l=new S({color:4342338}),m=new f(c,l);m.position.set(-3,-.2,-1),this.mesh.add(m);const d=new f(c,l);d.position.set(3,-.2,-1),this.mesh.add(d);const p=new x(.5,.7,1.5,6),y=new S({color:1713022,shininess:100}),u=new f(p,y);u.position.set(-2,0,3.5),u.rotation.x=Math.PI/2,u.castShadow=!0,this.mesh.add(u);const v=new f(p,y);v.position.set(2,0,3.5),v.rotation.x=Math.PI/2,v.castShadow=!0,this.mesh.add(v),this.createEnemyFlames();const E=new w(.3,8,8),k=new g({color:16711680}),L=new f(E,k);L.position.set(0,.8,-2),L.name="threatLight",this.mesh.add(L),this.createHealthBar(),this.mesh.position.copy(this.position)}createEnemyFlames(){const e=new E(.3,2,6),t=new g({color:16720384,transparent:!0,opacity:.7}),s=new f(e,t);s.position.set(-2,0,5.5),s.rotation.x=Math.PI/2,s.name="leftFlame",this.mesh.add(s);const i=new f(e,t);i.position.set(2,0,5.5),i.rotation.x=Math.PI/2,i.name="rightFlame",this.mesh.add(i)}createHealthBar(){const e=new k(3,.3),t=new g({color:3355443,transparent:!0,opacity:.8}),s=new f(e,t);s.position.set(0,2,0),s.name="healthBarBg",this.mesh.add(s);const i=new k(3,.25),o=new g({color:65280,transparent:!0,opacity:.9}),n=new f(i,o);n.position.set(0,2,.01),n.name="healthBar",this.mesh.add(n)}update(e,t){this.movementTime+=e,this.updateMovement(e,t),this.position.add(this.velocity.clone().multiplyScalar(e)),this.mesh.position.copy(this.position),this.boundingBox.setFromObject(this.mesh),this.projectiles.forEach(((t,s)=>{t.update(e),t.position.z>200&&this.projectiles.splice(s,1)})),this.updateShooting(t),this.updateAnimations(e),this.updateHealthBar()}updateAnimations(e){this.mesh.rotation.z=.08*Math.sin(2*this.movementTime),this.mesh.rotation.x=.05*Math.sin(1.5*this.movementTime);const t=this.mesh.getObjectByName("leftFlame"),s=this.mesh.getObjectByName("rightFlame");t&&s&&(t.scale.y=1+.3*Math.sin(8*this.movementTime),s.scale.y=1+.3*Math.sin(8*this.movementTime+1));const i=this.mesh.getObjectByName("threatLight");if(i){const e=i.material,t=.5+.3*Math.sin(6*this.movementTime);e.opacity=t}}updateHealthBar(){const e=this.mesh.getObjectByName("healthBar");if(e){const t=this.health/this.maxHealth;e.scale.x=t;const s=e.material;t>.6?s.color.setHex(65280):t>.3?s.color.setHex(16776960):s.color.setHex(16711680);const i=this.mesh.getObjectByName("healthBarBg");i&&(e.lookAt(0,0,0),i.lookAt(0,0,0))}}updateMovement(e,t){switch(this.movementPattern){case"straight":this.velocity.z=this.speed;break;case"zigzag":this.velocity.z=this.speed,this.velocity.x=Math.sin(3*this.movementTime)*this.speed*.5;break;case"circle":this.velocity.x=Math.cos(this.movementTime)*this.speed*.3,this.velocity.z=Math.sin(this.movementTime)*this.speed*.3+.5*this.speed}Math.abs(this.position.x)>80&&(this.velocity.x*=-.5)}updateShooting(e){if(Date.now()-this.lastShotTime<this.shotCooldown)return;const t=this.position.distanceTo(e);if(t<150&&t>20){const t=e.clone().sub(this.position).normalize();t.x+=.3*(Math.random()-.5),t.y+=.3*(Math.random()-.5),t.normalize(),this.shoot(t)}}shoot(e){const t=Date.now();if(t-this.lastShotTime<this.shotCooldown)return!1;this.lastShotTime=t;const s=e||new v(0,0,1);s.multiplyScalar(30);const i=new j(new v(this.position.x,this.position.y,this.position.z-2),s,!1);return this.projectiles.push(i),!0}takeDamage(e,t="normal"){this.health=Math.max(0,this.health-e),this.health>0?this.mesh.children.forEach((e=>{if(e instanceof f){const t=e.material,s=t.color.clone();t.color.setHex(16777215),setTimeout((()=>{t.color.copy(s)}),100)}})):this.createDestroyEffect(t)}createDestroyEffect(e){switch(e){case"missile":this.createMissileDestroyEffect();break;case"laser":this.createLaserDestroyEffect();break;default:this.createNormalDestroyEffect()}}createMissileDestroyEffect(){const e=()=>{this.mesh.rotation.x+=.3,this.mesh.rotation.y+=.2,this.mesh.rotation.z+=.4,this.mesh.scale.multiplyScalar(.95),this.createSparkEffect(),this.mesh.scale.x>.1&&requestAnimationFrame(e)};e()}createLaserDestroyEffect(){this.mesh.children.forEach(((e,t)=>{if(e instanceof f){const s=new v(2*(Math.random()-.5),2*(Math.random()-.5),2*(Math.random()-.5)).normalize(),i=()=>{e.position.add(s.clone().multiplyScalar(.5)),e.rotation.x+=.1,e.rotation.y+=.1,e.scale.multiplyScalar(.98),e.scale.x>.1&&requestAnimationFrame(i)};setTimeout((()=>i()),50*t)}}))}createNormalDestroyEffect(){const e=()=>{this.mesh.children.forEach((e=>{if(e instanceof f){const t=e.material;t.transparent=!0,t.opacity=Math.max(0,t.opacity-.05)}})),this.mesh.scale.multiplyScalar(1.02),this.mesh.scale.x<1.5&&requestAnimationFrame(e)};e()}createSparkEffect(){for(let e=0;e<8;e++){const t=new f(new w(.1,4,4),new g({color:16755200,transparent:!0,opacity:1}));t.position.copy(this.position),t.position.add(new v(4*(Math.random()-.5),4*(Math.random()-.5),4*(Math.random()-.5)));const s=()=>{t.position.y-=.1;const e=t.material;e.opacity-=.02,e.opacity>0?requestAnimationFrame(s):t.userData.shouldRemove=!0};t.userData.isSpark=!0,this.mesh.add(t),setTimeout((()=>s()),100*e)}}isAlive(){return this.health>0}isOutOfBounds(){return this.position.z>200||Math.abs(this.position.x)>200||Math.abs(this.position.y)>100}getProjectiles(){return this.projectiles}removeProjectile(e){const t=this.projectiles.indexOf(e);t>-1&&this.projectiles.splice(t,1)}getBoundingBox(){return this.boundingBox}getScore(){switch(this.movementPattern){case"straight":default:return 100;case"zigzag":return 150;case"circle":return 200}}}class D{constructor(e,s,i){t(this,"crosshairElement"),t(this,"targetIndicators",[]),t(this,"camera"),t(this,"raycaster"),t(this,"mouse"),t(this,"canvas"),t(this,"isAiming",!1),t(this,"zoomLevel",1),t(this,"maxZoom",3),t(this,"minZoom",1),this.camera=e,this.canvas=i,this.raycaster=new L,this.mouse=new T,this.createCrosshair(),this.initEventListeners()}createCrosshair(){this.crosshairElement=document.createElement("div"),this.crosshairElement.id="crosshair",this.crosshairElement.innerHTML='\n      <div class="crosshair-center"></div>\n      <div class="crosshair-line crosshair-top"></div>\n      <div class="crosshair-line crosshair-bottom"></div>\n      <div class="crosshair-line crosshair-left"></div>\n      <div class="crosshair-line crosshair-right"></div>\n      <div class="crosshair-circle"></div>\n      <div class="zoom-indicator">\n        <span class="zoom-text">1.0x</span>\n      </div>\n    ',document.body.appendChild(this.crosshairElement),this.addCrosshairStyles()}addCrosshairStyles(){const e=document.createElement("style");e.textContent="\n      #crosshair {\n        position: fixed;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        pointer-events: none;\n        z-index: 1000;\n        transition: all 0.3s ease;\n      }\n\n      .crosshair-center {\n        width: 4px;\n        height: 4px;\n        background: #00ff00;\n        border-radius: 50%;\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        box-shadow: 0 0 10px #00ff00;\n      }\n\n      .crosshair-line {\n        background: #00ff00;\n        position: absolute;\n        box-shadow: 0 0 5px #00ff00;\n      }\n\n      .crosshair-top, .crosshair-bottom {\n        width: 2px;\n        height: 20px;\n        left: 50%;\n        transform: translateX(-50%);\n      }\n\n      .crosshair-top {\n        top: -30px;\n      }\n\n      .crosshair-bottom {\n        bottom: -30px;\n      }\n\n      .crosshair-left, .crosshair-right {\n        width: 20px;\n        height: 2px;\n        top: 50%;\n        transform: translateY(-50%);\n      }\n\n      .crosshair-left {\n        left: -30px;\n      }\n\n      .crosshair-right {\n        right: -30px;\n      }\n\n      .crosshair-circle {\n        width: 80px;\n        height: 80px;\n        border: 1px solid #00ff00;\n        border-radius: 50%;\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%);\n        opacity: 0.6;\n        box-shadow: 0 0 15px rgba(0, 255, 0, 0.3);\n      }\n\n      .zoom-indicator {\n        position: absolute;\n        top: -60px;\n        left: 50%;\n        transform: translateX(-50%);\n        background: rgba(0, 0, 0, 0.8);\n        padding: 5px 10px;\n        border-radius: 5px;\n        border: 1px solid #00ff00;\n      }\n\n      .zoom-text {\n        color: #00ff00;\n        font-size: 14px;\n        font-weight: bold;\n        text-shadow: 0 0 5px #00ff00;\n      }\n\n      #crosshair.aiming {\n        transform: translate(-50%, -50%) scale(1.2);\n      }\n\n      #crosshair.aiming .crosshair-center {\n        background: #ff4400;\n        box-shadow: 0 0 15px #ff4400;\n      }\n\n      #crosshair.aiming .crosshair-line {\n        background: #ff4400;\n        box-shadow: 0 0 8px #ff4400;\n      }\n\n      #crosshair.aiming .crosshair-circle {\n        border-color: #ff4400;\n        box-shadow: 0 0 20px rgba(255, 68, 0, 0.5);\n      }\n\n      .target-indicator {\n        position: fixed;\n        width: 30px;\n        height: 30px;\n        border: 2px solid #ff0000;\n        border-radius: 50%;\n        pointer-events: none;\n        z-index: 999;\n        animation: targetPulse 1s infinite;\n        transform: translate(-50%, -50%);\n      }\n\n      @keyframes targetPulse {\n        0%, 100% { \n          transform: translate(-50%, -50%) scale(1);\n          opacity: 1;\n        }\n        50% { \n          transform: translate(-50%, -50%) scale(1.2);\n          opacity: 0.7;\n        }\n      }\n\n      .distance-indicator {\n        position: absolute;\n        top: -25px;\n        left: 50%;\n        transform: translateX(-50%);\n        background: rgba(0, 0, 0, 0.8);\n        color: #ff0000;\n        padding: 2px 6px;\n        border-radius: 3px;\n        font-size: 10px;\n        font-weight: bold;\n        white-space: nowrap;\n      }\n    ",document.head.appendChild(e)}initEventListeners(){this.canvas.addEventListener("mousemove",(e=>{this.updateMousePosition(e)})),this.canvas.addEventListener("mousedown",(e=>{2===e.button&&this.startAiming()})),this.canvas.addEventListener("mouseup",(e=>{2===e.button&&this.stopAiming()})),this.canvas.addEventListener("wheel",(e=>{e.preventDefault(),this.handleZoom(e.deltaY)})),this.canvas.addEventListener("contextmenu",(e=>{e.preventDefault()}))}updateMousePosition(e){const t=this.canvas.getBoundingClientRect();this.mouse.x=(e.clientX-t.left)/t.width*2-1,this.mouse.y=-(e.clientY-t.top)/t.height*2+1}startAiming(){this.isAiming=!0,this.crosshairElement.classList.add("aiming"),this.camera.fov=75/this.zoomLevel,this.camera.updateProjectionMatrix()}stopAiming(){this.isAiming=!1,this.crosshairElement.classList.remove("aiming"),this.camera.fov=75,this.camera.updateProjectionMatrix()}handleZoom(e){if(!this.isAiming)return;this.zoomLevel=e>0?Math.max(this.minZoom,this.zoomLevel-.1):Math.min(this.maxZoom,this.zoomLevel+.1),this.camera.fov=75/this.zoomLevel,this.camera.updateProjectionMatrix();const t=this.crosshairElement.querySelector(".zoom-text");t&&(t.textContent=`${this.zoomLevel.toFixed(1)}x`)}update(e,t){if(this.clearTargetIndicators(),this.updateCrosshairAnimation(),!this.isAiming)return void this.showNearbyEnemies(e,t);this.raycaster.setFromCamera(this.mouse,this.camera);let s=null,i=1/0;e.forEach((e=>{const t=this.raycaster.intersectObject(e.mesh,!0);if(t.length>0){const o=t[0].distance;o<i&&(i=o,s=e),this.createTargetIndicator(e,o,o===i)}})),this.updateAimingAccuracy(s,i)}createTargetIndicator(e,t,s=!1){const i=e.position.clone();i.project(this.camera);const o=(.5*i.x+.5)*window.innerWidth,n=(-.5*i.y+.5)*window.innerHeight,a=document.createElement("div");a.className=s?"target-indicator primary-target":"target-indicator",a.style.left=`${o}px`,a.style.top=`${n}px`,s&&(a.style.borderColor="#ff0000",a.style.borderWidth="3px",a.style.boxShadow="0 0 20px rgba(255, 0, 0, 0.8)");const r=document.createElement("div");r.className="target-info",r.innerHTML=`\n      <div class="distance">${Math.round(t)}m</div>\n      <div class="health">${Math.round(e.health)}HP</div>\n    `,r.style.cssText=`\n      position: absolute;\n      top: -40px;\n      left: 50%;\n      transform: translateX(-50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: ${s?"#ff0000":"#ffffff"};\n      padding: 2px 6px;\n      border-radius: 3px;\n      font-size: 10px;\n      font-weight: bold;\n      white-space: nowrap;\n      text-align: center;\n    `,a.appendChild(r),document.body.appendChild(a),this.targetIndicators.push(a)}clearTargetIndicators(){this.targetIndicators.forEach((e=>{e.parentNode&&e.parentNode.removeChild(e)})),this.targetIndicators=[]}getAimingDirection(){return this.raycaster.setFromCamera(this.mouse,this.camera),this.raycaster.ray.direction.clone()}isCurrentlyAiming(){return this.isAiming}getZoomLevel(){return this.zoomLevel}updateCrosshairAnimation(){const e=.001*Date.now(),t=1+.02*Math.sin(2*e);this.crosshairElement.style.transform=`translate(-50%, -50%) scale(${t})`;const s=this.crosshairElement.querySelector(".crosshair-center");s&&(s.style.opacity=(.8+.2*Math.sin(4*e)).toString())}showNearbyEnemies(e,t){t&&e.forEach((e=>{const s=e.position.distanceTo(t);s<200&&this.createDirectionIndicator(e,s)}))}createDirectionIndicator(e,t){const s=e.position.clone();s.project(this.camera);let i=(.5*s.x+.5)*window.innerWidth,o=(-.5*s.y+.5)*window.innerHeight;i=Math.max(50,Math.min(window.innerWidth-50,i)),o=Math.max(50,Math.min(window.innerHeight-50,o));const n=document.createElement("div");n.className="direction-indicator",n.style.cssText=`\n      position: fixed;\n      left: ${i}px;\n      top: ${o}px;\n      width: 20px;\n      height: 20px;\n      border: 2px solid #ffaa00;\n      border-radius: 50%;\n      transform: translate(-50%, -50%);\n      pointer-events: none;\n      z-index: 998;\n      opacity: ${Math.max(.3,1-t/200)};\n    `,document.body.appendChild(n),this.targetIndicators.push(n)}updateAimingAccuracy(e,t){if(!this.crosshairElement.querySelector(".accuracy-indicator")){const e=document.createElement("div");e.className="accuracy-indicator",e.style.cssText="\n        position: absolute;\n        bottom: -80px;\n        left: 50%;\n        transform: translateX(-50%);\n        background: rgba(0, 0, 0, 0.8);\n        padding: 5px 10px;\n        border-radius: 5px;\n        border: 1px solid #00ff00;\n        color: #00ff00;\n        font-size: 12px;\n        font-weight: bold;\n        text-align: center;\n        min-width: 100px;\n      ",this.crosshairElement.appendChild(e)}const s=this.crosshairElement.querySelector(".accuracy-indicator");if(e&&t<300){const e=Math.max(20,100-t/3);s.textContent=`命中率: ${Math.round(e)}%`,s.style.color=e>70?"#00ff00":e>40?"#ffff00":"#ff4400",s.style.borderColor=s.style.color}else s.textContent="无目标",s.style.color="#666666",s.style.borderColor="#666666"}dispose(){this.clearTargetIndicators(),this.crosshairElement.parentNode&&this.crosshairElement.parentNode.removeChild(this.crosshairElement)}}class B{constructor(e,s,i){t(this,"keys",{}),t(this,"mouse",{x:0,y:0}),t(this,"player"),t(this,"camera"),t(this,"canvas"),t(this,"crosshair",null),t(this,"isMouseLocked",!1),this.player=e,this.camera=s,this.canvas=i,this.initEventListeners()}setCrosshair(e){this.crosshair=e}initEventListeners(){document.addEventListener("keydown",(e=>this.onKeyDown(e))),document.addEventListener("keyup",(e=>this.onKeyUp(e))),this.canvas.addEventListener("click",(()=>this.requestPointerLock())),document.addEventListener("pointerlockchange",(()=>this.onPointerLockChange())),document.addEventListener("mousemove",(e=>this.onMouseMove(e))),this.canvas.addEventListener("contextmenu",(e=>e.preventDefault()))}onKeyDown(e){this.keys[e.code.toLowerCase()]=!0,["Space","ArrowUp","ArrowDown","ArrowLeft","ArrowRight"].includes(e.code)&&e.preventDefault()}onKeyUp(e){this.keys[e.code.toLowerCase()]=!1}onMouseMove(e){if(!this.isMouseLocked)return;this.mouse.x+=.002*e.movementX,this.mouse.y+=.002*e.movementY,this.mouse.x=Math.max(-Math.PI/4,Math.min(Math.PI/4,this.mouse.x)),this.mouse.y=Math.max(-Math.PI/6,Math.min(Math.PI/6,this.mouse.y))}requestPointerLock(){this.canvas.requestPointerLock()}onPointerLockChange(){this.isMouseLocked=document.pointerLockElement===this.canvas}update(){this.handleMovement(),this.handleShooting(),this.handleCamera()}handleMovement(){(this.keys.keyw||this.keys.arrowup)&&this.player.moveForward(),(this.keys.keys||this.keys.arrowdown)&&this.player.moveBackward(),(this.keys.keya||this.keys.arrowleft)&&this.player.moveLeft(),(this.keys.keyd||this.keys.arrowright)&&this.player.moveRight(),this.keys.keyq&&this.player.moveUp(),this.keys.keye&&this.player.moveDown()}handleShooting(){if(this.keys.space){let e;this.crosshair&&this.crosshair.isCurrentlyAiming()&&(e=this.crosshair.getAimingDirection()),this.player.shoot(e)}this.keys.digit1&&this.player.useSkill("rapidFire"),this.keys.digit2&&this.player.useSkill("laserBeam"),this.keys.digit3&&this.player.useSkill("missile"),this.keys.digit4&&this.player.useSkill("shield")}handleCamera(){const e=this.player.position,t=new v(0,20,50);this.isMouseLocked&&(t.x+=30*this.mouse.x,t.y+=20*this.mouse.y);const s=e.clone().add(t);this.camera.position.lerp(s,.1);const i=e.clone().add(new v(10*this.mouse.x,5*this.mouse.y,-20));this.camera.lookAt(i)}getControlsInfo(){return["WASD / 方向键 - 移动飞机","Q/E - 上升/下降","空格键 - 发射炮弹","右键 - 瞄准模式","滚轮 - 缩放瞄准镜","1 - 快速射击技能","2 - 激光束技能","3 - 导弹技能","4 - 护盾技能","鼠标 - 控制视角","点击画面锁定鼠标"]}isKeyPressed(e){return this.keys[e.toLowerCase()]||!1}getMousePosition(){return{...this.mouse}}getMouseLockStatus(){return this.isMouseLocked}exitPointerLock(){this.isMouseLocked&&document.exitPointerLock()}reset(){this.keys={},this.mouse={x:0,y:0},this.exitPointerLock()}dispose(){document.removeEventListener("keydown",this.onKeyDown),document.removeEventListener("keyup",this.onKeyUp),document.removeEventListener("pointerlockchange",this.onPointerLockChange),document.removeEventListener("mousemove",this.onMouseMove),this.canvas.removeEventListener("click",this.requestPointerLock),this.canvas.removeEventListener("contextmenu",(e=>e.preventDefault()))}}new class{constructor(){t(this,"gameWorld"),t(this,"player"),t(this,"enemies",[]),t(this,"controls"),t(this,"crosshair"),t(this,"gameRunning",!1),t(this,"score",0),t(this,"enemySpawnTimer",0),t(this,"enemySpawnInterval",2e3),t(this,"lastTime",0),t(this,"gameStartTime",0),t(this,"scoreElement"),t(this,"healthElement"),t(this,"energyElement"),t(this,"skillElements",{}),this.initGame()}initGame(){const e=document.querySelector("#game-canvas");if(!e)throw new Error("无法找到游戏画布元素 #game-canvas");this.gameWorld=new F(e),this.player=new C,this.gameWorld.addToScene(this.player.mesh),this.controls=new B(this.player,this.gameWorld.camera,e),this.crosshair=new D(this.gameWorld.camera,this.gameWorld.scene,e),this.controls.setCrosshair(this.crosshair),this.scoreElement=document.querySelector("#score"),this.healthElement=document.querySelector("#health"),this.energyElement=document.querySelector("#energy"),this.skillElements={rapidFire:document.querySelector("#skill-1"),laserBeam:document.querySelector("#skill-2"),missile:document.querySelector("#skill-3"),shield:document.querySelector("#skill-4")},window.addEventListener("resize",(()=>this.gameWorld.onWindowResize())),this.startGame()}startGame(){this.gameRunning=!0,this.gameStartTime=Date.now(),this.lastTime=performance.now(),this.gameLoop(),console.log("太空战斗游戏已启动！"),console.log("控制说明："),console.log("WASD - 移动飞机"),console.log("Q/E - 上升/下降"),console.log("空格键 - 发射炮弹"),console.log("鼠标 - 控制视角（点击画面锁定鼠标）")}gameLoop(){if(!this.gameRunning)return;const e=performance.now(),t=(e-this.lastTime)/1e3;this.lastTime=e,this.controls.update(),this.player.update(t),this.updateEnemies(t),this.crosshair.update(this.enemies,this.player.position),this.spawnEnemies(),this.checkCollisions(),this.cleanup(),this.updateUI(),this.gameWorld.render(),requestAnimationFrame((()=>this.gameLoop()))}updateEnemies(e){for(let t=this.enemies.length-1;t>=0;t--){const s=this.enemies[t];s.update(e,this.player.position),s.isAlive()&&!s.isOutOfBounds()||(this.gameWorld.removeFromScene(s.mesh),s.isAlive()||(this.score+=s.getScore(),console.log(`敌机被击毁！得分 +${s.getScore()}，总分: ${this.score}`)),this.enemies.splice(t,1))}}spawnEnemies(){const e=Date.now();if(e-this.enemySpawnTimer>this.enemySpawnInterval){this.enemySpawnTimer=e;const t=150*(Math.random()-.5),s=50*(Math.random()-.5),i=-200,o=["straight","zigzag","circle"],n=o[Math.floor(Math.random()*o.length)],a=new A(new v(t,s,i),n);this.enemies.push(a),this.gameWorld.addToScene(a.mesh);const r=(e-this.gameStartTime)/1e3;this.enemySpawnInterval=Math.max(500,2e3-10*r)}}checkCollisions(){for(let e=this.player.getProjectiles().length-1;e>=0;e--){const t=this.player.getProjectiles()[e];for(let e=this.enemies.length-1;e>=0;e--){const s=this.enemies[e];if(t.checkCollision(s.mesh)){const e=this.getWeaponTypeFromProjectile(t);s.takeDamage(t.damage,e),this.gameWorld.removeFromScene(t.mesh),this.player.removeProjectile(t),"missile"===e?this.createExplosion(t.position,!0):this.createExplosion(t.position),s.isAlive()||this.createEnemyDestroyEffect(s.position,e);break}}}this.enemies.forEach((e=>{for(let t=e.getProjectiles().length-1;t>=0;t--){const s=e.getProjectiles()[t];s.checkCollision(this.player.mesh)&&(this.player.takeDamage(s.damage),this.gameWorld.removeFromScene(s.mesh),e.removeProjectile(s),this.createExplosion(s.position))}})),this.enemies.forEach((e=>{e.position.distanceTo(this.player.position)<8&&(this.player.takeDamage(50),e.takeDamage(100),this.createExplosion(e.position,!0),this.createScreenShake())})),this.player.isAlive()||this.gameOver()}createScreenShake(){const e=this.gameWorld.camera,t=e.position.clone();let s=0;const i=()=>{if(s<.5){const o=2*(Math.random()-.5),n=2*(Math.random()-.5);e.position.x=t.x+o,e.position.y=t.y+n,s+=.016,requestAnimationFrame(i)}else e.position.copy(t)};i()}cleanup(){this.player.getProjectiles().forEach((e=>{e.isOutOfBounds()?(this.gameWorld.removeFromScene(e.mesh),this.player.removeProjectile(e)):this.gameWorld.scene.children.includes(e.mesh)||this.gameWorld.addToScene(e.mesh)})),this.enemies.forEach((e=>{e.getProjectiles().forEach((t=>{t.isOutOfBounds()?(this.gameWorld.removeFromScene(t.mesh),e.removeProjectile(t)):this.gameWorld.scene.children.includes(t.mesh)||this.gameWorld.addToScene(t.mesh)}))}))}updateUI(){this.scoreElement.textContent=`得分: ${this.score}`,this.healthElement.textContent=`生命值: ${Math.round(this.player.health)}`,this.energyElement.textContent=`能量: ${Math.round(this.player.energy)}`,this.player.health>60?this.healthElement.style.color="#00ff00":this.player.health>30?this.healthElement.style.color="#ffff00":this.healthElement.style.color="#ff0000",this.updateSkillsUI()}updateSkillsUI(){["rapidFire","laserBeam","missile","shield"].forEach((e=>{const t=this.skillElements[e];if(!t)return;const s=this.player.canUseSkill(e),i=this.player.getSkillCooldown(e),o=this.player.skills[e];t.className="skill-slot",s?t.classList.add("available"):i>0&&t.classList.add("cooldown");const n=t.querySelector(".skill-cooldown");if(n&&o){const e=i/o.cooldown;n.style.transform=`scaleX(${e})`,n.style.display=i>0?"block":"none"}}))}createExplosion(e,t=!1){const s=t?20:10,i=[];for(let n=0;n<s;n++){const s=new w(t?.5:.2,4,4),o=new g({color:t?16729088:16755200,transparent:!0,opacity:.8}),n=new f(s,o);n.position.copy(e);const a=new v(2*(Math.random()-.5),2*(Math.random()-.5),2*(Math.random()-.5)).normalize(),r=Math.random()*(t?20:10)+5;n.userData={velocity:a.multiplyScalar(r),life:1,maxLife:t?2:1},i.push(n),this.gameWorld.addToScene(n)}const o=()=>{i.forEach(((e,t)=>{if(e.userData.life<=0)return this.gameWorld.removeFromScene(e),void i.splice(t,1);e.position.add(e.userData.velocity.clone().multiplyScalar(.016)),e.userData.life-=.016;e.material.opacity=e.userData.life/e.userData.maxLife,e.userData.velocity.multiplyScalar(.98)})),i.length>0&&requestAnimationFrame(o)};o()}gameOver(){this.gameRunning=!1;const e=document.createElement("div");e.style.cssText="\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      background: rgba(0, 0, 0, 0.8);\n      color: white;\n      padding: 30px;\n      border-radius: 10px;\n      text-align: center;\n      font-size: 24px;\n      z-index: 1000;\n    ",e.innerHTML=`\n      <h2 style="color: #ff4444; margin-bottom: 20px;">游戏结束</h2>\n      <p>最终得分: ${this.score}</p>\n      <button onclick="location.reload()" style="\n        margin-top: 20px;\n        padding: 10px 20px;\n        font-size: 18px;\n        background: #0088ff;\n        color: white;\n        border: none;\n        border-radius: 5px;\n        cursor: pointer;\n      ">重新开始</button>\n    `,document.body.appendChild(e),console.log(`游戏结束！最终得分: ${this.score}`)}getWeaponTypeFromProjectile(e){return e.damage>=80?"missile":e.damage>=40?"laser":"normal"}createEnemyDestroyEffect(e,t){switch(t){case"missile":this.createMissileDestroyEffect(e);break;case"laser":this.createLaserDestroyEffect(e);break;default:this.createNormalDestroyEffect(e)}}createMissileDestroyEffect(e){const t=new P(0,1,16),s=new g({color:16729088,transparent:!0,opacity:.8,side:z}),i=new f(t,s);i.position.copy(e),this.gameWorld.addToScene(i);const o=()=>{i.scale.multiplyScalar(1.1),s.opacity-=.02,s.opacity>0?requestAnimationFrame(o):this.gameWorld.removeFromScene(i)};o(),this.createFireball(e)}createLaserDestroyEffect(e){const t=new f(new w(2,8,8),new g({color:65535,transparent:!0,opacity:.8}));t.position.copy(e),this.gameWorld.addToScene(t);const s=()=>{t.scale.multiplyScalar(1.05);const e=t.material;e.opacity-=.03,e.opacity>0?requestAnimationFrame(s):this.gameWorld.removeFromScene(t)};s()}createNormalDestroyEffect(e){for(let t=0;t<6;t++){const s=new f(new b(.3,.3,.3),new g({color:6710886,transparent:!0,opacity:1}));s.position.copy(e);const i=new v(2*(Math.random()-.5),2*(Math.random()-.5),2*(Math.random()-.5)).normalize();this.gameWorld.addToScene(s);const o=()=>{s.position.add(i.clone().multiplyScalar(.3)),s.rotation.x+=.1,s.rotation.y+=.1;const e=s.material;e.opacity-=.02,e.opacity>0?requestAnimationFrame(o):this.gameWorld.removeFromScene(s)};setTimeout((()=>o()),50*t)}}createFireball(e){const t=new f(new w(3,12,12),new g({color:16737792,transparent:!0,opacity:.9}));t.position.copy(e),this.gameWorld.addToScene(t);const s=()=>{t.scale.multiplyScalar(1.03);const e=t.material;e.opacity-=.025,e.opacity>.5?e.color.setHex(16737792):e.opacity>.2?e.color.setHex(16711680):e.color.setHex(3342336),e.opacity>0?requestAnimationFrame(s):this.gameWorld.removeFromScene(t)};s()}};
