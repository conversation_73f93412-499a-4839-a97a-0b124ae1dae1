# 简化的 GitHub Pages 部署工作流
# 用于解决权限和配置问题
# 作者: 9531lyj

name: 🚀 简化部署到 GitHub Pages

# 触发条件
on:
  # 手动触发 - 用于测试
  workflow_dispatch:
  # 推送到 main 分支时触发
  push:
    branches: [ main ]

# 设置权限
permissions:
  contents: read
  pages: write
  id-token: write

# 并发控制
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # 使用传统方式部署
  deploy:
    name: 🔨 构建并部署
    runs-on: ubuntu-latest
    
    steps:
    # 检出代码
    - name: 📥 检出代码
      uses: actions/checkout@v4
    
    # 设置 Node.js
    - name: 🟢 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    # 安装依赖
    - name: 📦 安装依赖
      run: |
        echo "安装项目依赖..."
        npm ci
        echo "依赖安装完成"
    
    # 构建项目
    - name: 🏗️ 构建项目
      env:
        NODE_ENV: production
      run: |
        echo "开始构建..."
        npm run build
        echo "构建完成"
        echo "构建产物:"
        ls -la dist/
    
    # 使用 gh-pages 部署（备用方案）
    - name: 🚀 部署到 GitHub Pages (gh-pages)
      run: |
        echo "配置 Git..."
        git config --global user.name "9531lyj"
        git config --global user.email "<EMAIL>"
        
        echo "安装 gh-pages..."
        npm install -g gh-pages
        
        echo "部署到 gh-pages 分支..."
        npx gh-pages -d dist -u "9531lyj <<EMAIL>>"
        
        echo "部署完成！"
        echo "游戏地址: https://9531lyj.github.io/space-battle-game/"
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    
    # 部署成功通知
    - name: 🎉 部署成功
      run: |
        echo "🎉 太空战斗游戏部署成功！"
        echo "🌐 游戏地址: https://9531lyj.github.io/space-battle-game/"
        echo "📅 部署时间: $(date)"
        echo "👨‍💻 作者: 9531lyj"
        echo "📧 邮箱: <EMAIL>"
