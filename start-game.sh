#!/bin/bash

# 太空战斗游戏启动脚本
# 作者: 9531lyj

echo "🚀 太空战斗游戏启动脚本"
echo "=========================="
echo ""

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未检测到Node.js"
    echo "请先安装Node.js (https://nodejs.org/)"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 未检测到npm"
    echo "请先安装npm"
    exit 1
fi

echo "✅ Node.js版本: $(node --version)"
echo "✅ npm版本: $(npm --version)"
echo ""

# 检查package.json是否存在
if [ ! -f "package.json" ]; then
    echo "❌ 错误: 未找到package.json文件"
    echo "请确保在项目根目录运行此脚本"
    exit 1
fi

echo "📦 正在安装依赖..."
if npm install; then
    echo "✅ 依赖安装成功"
else
    echo "❌ 依赖安装失败"
    exit 1
fi

echo ""
echo "🚀 启动开发服务器..."
echo "游戏将在浏览器中自动打开"
echo "如果没有自动打开，请手动访问显示的地址"
echo ""

# 启动开发服务器
npm run dev
