<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/space-battle-game/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>太空战斗游戏 - Space Battle Game</title>
    <script type="module" crossorigin src="/space-battle-game/assets/index-BlQPMwIO.js"></script>
    <link rel="modulepreload" crossorigin href="/space-battle-game/assets/three-DKRBiI99.js">
    <link rel="stylesheet" crossorigin href="/space-battle-game/assets/index-Bh7lPSKn.css">
  </head>
  <body>
    <div id="game-container">
      <div id="ui-overlay">
        <div id="player-stats">
          <div id="score">得分: 0</div>
          <div id="health">生命值: 100</div>
          <div id="energy">能量: 100</div>
        </div>

        <div id="skills-panel">
          <div class="skill-slot" id="skill-1">
            <div class="skill-key">1</div>
            <div class="skill-name">快速射击</div>
            <div class="skill-cooldown"></div>
          </div>
          <div class="skill-slot" id="skill-2">
            <div class="skill-key">2</div>
            <div class="skill-name">激光束</div>
            <div class="skill-cooldown"></div>
          </div>
          <div class="skill-slot" id="skill-3">
            <div class="skill-key">3</div>
            <div class="skill-name">导弹</div>
            <div class="skill-cooldown"></div>
          </div>
          <div class="skill-slot" id="skill-4">
            <div class="skill-key">4</div>
            <div class="skill-name">护盾</div>
            <div class="skill-cooldown"></div>
          </div>
        </div>

        <div id="instructions">
          <p>🎮 游戏控制</p>
          <p><strong>WASD</strong> - 移动飞机</p>
          <p><strong>Q/E</strong> - 上升/下降</p>
          <p><strong>空格</strong> - 发射炮弹</p>
          <p><strong>右键</strong> - 瞄准模式</p>
          <p><strong>滚轮</strong> - 缩放瞄准镜</p>
          <p><strong>1234</strong> - 使用技能</p>
          <p><strong>鼠标</strong> - 控制视角</p>
        </div>
      </div>
      <canvas id="game-canvas"></canvas>
    </div>
  </body>
</html>
