// 导入样式文件
import './style.css';
// 导入Three.js 3D图形库
import * as THREE from 'three';
// 导入游戏核心模块
import { GameWorld } from './game/GameWorld';    // 游戏世界管理器
import { Player } from './game/Player';          // 玩家系统
import { Enemy } from './game/Enemy';            // 敌机AI系统
import { Controls } from './game/Controls';      // 控制系统
import { Crosshair } from './game/Crosshair';   // 瞄准镜系统

/**
 * 太空战斗游戏主类
 * 负责游戏的整体流程控制、对象管理和游戏循环
 *
 * 功能特性:
 * - 3D太空环境渲染
 * - 玩家飞机控制系统
 * - 敌机AI和生成系统
 * - 碰撞检测和物理系统
 * - 技能系统和UI管理
 * - 粒子特效和爆炸效果
 *
 * <AUTHOR>
 * @version 2.0
 */
class SpaceBattleGame {
  // 核心游戏对象
  private gameWorld!: GameWorld;     // 3D游戏世界管理器
  private player!: Player;           // 玩家飞机对象
  private enemies: Enemy[] = [];    // 敌机数组
  private controls!: Controls;       // 输入控制系统
  private crosshair!: Crosshair;     // 瞄准镜系统

  // 游戏状态管理
  private gameRunning: boolean = false;        // 游戏运行状态
  private score: number = 0;                   // 玩家得分
  private enemySpawnTimer: number = 0;         // 敌机生成计时器
  private enemySpawnInterval: number = 2000;   // 敌机生成间隔(毫秒) - 2秒生成一个敌机
  private lastTime: number = 0;                // 上一帧时间戳
  private gameStartTime: number = 0;           // 游戏开始时间

  // UI界面元素引用
  private scoreElement!: HTMLElement;                        // 得分显示元素
  private healthElement!: HTMLElement;                       // 生命值显示元素
  private energyElement!: HTMLElement;                       // 能量显示元素
  private skillElements: { [key: string]: HTMLElement } = {}; // 技能UI元素集合

  /**
   * 构造函数 - 初始化游戏
   */
  constructor() {
    this.initGame();
  }

  /**
   * 初始化游戏 - 设置所有游戏组件和系统
   *
   * 初始化流程:
   * 1. 获取HTML画布元素
   * 2. 创建3D游戏世界
   * 3. 初始化玩家飞机
   * 4. 设置控制系统
   * 5. 配置瞄准镜系统
   * 6. 绑定UI元素
   * 7. 注册事件监听器
   * 8. 启动游戏循环
   */
  private initGame(): void {
    // 获取HTML5 Canvas画布元素，用于3D渲染
    const canvas = document.querySelector<HTMLCanvasElement>('#game-canvas')!;
    if (!canvas) {
      throw new Error('无法找到游戏画布元素 #game-canvas');
    }

    // 初始化3D游戏世界 (场景、相机、渲染器、光照、星空背景)
    this.gameWorld = new GameWorld(canvas);

    // 创建玩家飞机对象并添加到3D场景中
    this.player = new Player();
    this.gameWorld.addToScene(this.player.mesh);

    // 初始化输入控制系统 (键盘、鼠标控制)
    this.controls = new Controls(this.player, this.gameWorld.camera, canvas);

    // 初始化瞄准镜系统 (准星、目标锁定、缩放功能)
    this.crosshair = new Crosshair(this.gameWorld.camera, this.gameWorld.scene, canvas);

    // 将瞄准镜系统连接到控制系统，实现瞄准功能
    this.controls.setCrosshair(this.crosshair);

    // 获取并绑定游戏UI元素
    this.scoreElement = document.querySelector('#score')!;
    this.healthElement = document.querySelector('#health')!;
    this.energyElement = document.querySelector('#energy')!;

    // 获取技能UI元素 - 对应四个技能按钮
    this.skillElements = {
      'rapidFire': document.querySelector('#skill-1')!,  // 快速射击技能
      'laserBeam': document.querySelector('#skill-2')!,  // 激光束技能
      'missile': document.querySelector('#skill-3')!,    // 导弹技能
      'shield': document.querySelector('#skill-4')!      // 护盾技能
    };

    // 注册窗口大小调整事件监听器，确保游戏画面自适应
    window.addEventListener('resize', () => this.gameWorld.onWindowResize());

    // 启动游戏主循环
    this.startGame();
  }

  private startGame(): void {
    this.gameRunning = true;
    this.gameStartTime = Date.now();
    this.lastTime = performance.now();
    this.gameLoop();

    console.log('太空战斗游戏已启动！');
    console.log('控制说明：');
    console.log('WASD - 移动飞机');
    console.log('Q/E - 上升/下降');
    console.log('空格键 - 发射炮弹');
    console.log('鼠标 - 控制视角（点击画面锁定鼠标）');
  }

  private gameLoop(): void {
    if (!this.gameRunning) return;

    const currentTime = performance.now();
    const deltaTime = (currentTime - this.lastTime) / 1000; // 转换为秒
    this.lastTime = currentTime;

    // 更新控制
    this.controls.update();

    // 更新玩家
    this.player.update(deltaTime);

    // 更新敌机
    this.updateEnemies(deltaTime);

    // 更新瞄准镜
    this.crosshair.update(this.enemies, this.player.position);

    // 生成新敌机
    this.spawnEnemies();

    // 检查碰撞
    this.checkCollisions();

    // 清理超出边界的对象
    this.cleanup();

    // 更新UI
    this.updateUI();

    // 渲染场景
    this.gameWorld.render();

    // 继续游戏循环
    requestAnimationFrame(() => this.gameLoop());
  }

  private updateEnemies(deltaTime: number): void {
    // 使用倒序遍历避免数组索引问题
    for (let index = this.enemies.length - 1; index >= 0; index--) {
      const enemy = this.enemies[index];
      enemy.update(deltaTime, this.player.position);

      // 检查敌机是否需要移除
      if (!enemy.isAlive() || enemy.isOutOfBounds()) {
        // 从场景中移除敌机
        this.gameWorld.removeFromScene(enemy.mesh);

        // 如果敌机被击毁（而不是超出边界），增加分数
        if (!enemy.isAlive()) {
          this.score += enemy.getScore();
          console.log(`敌机被击毁！得分 +${enemy.getScore()}，总分: ${this.score}`);
        }

        // 从敌机数组中移除
        this.enemies.splice(index, 1);
      }
    }
  }

  private spawnEnemies(): void {
    const currentTime = Date.now();
    if (currentTime - this.enemySpawnTimer > this.enemySpawnInterval) {
      this.enemySpawnTimer = currentTime;

      // 随机生成敌机位置
      const x = (Math.random() - 0.5) * 150;
      const y = (Math.random() - 0.5) * 50;
      const z = -200;

      // 随机选择移动模式
      const patterns: ('straight' | 'zigzag' | 'circle')[] = ['straight', 'zigzag', 'circle'];
      const pattern = patterns[Math.floor(Math.random() * patterns.length)];

      const enemy = new Enemy(new THREE.Vector3(x, y, z), pattern);
      this.enemies.push(enemy);
      this.gameWorld.addToScene(enemy.mesh);

      // 随着时间推移，增加敌机生成频率
      const gameTime = (currentTime - this.gameStartTime) / 1000;
      this.enemySpawnInterval = Math.max(500, 2000 - gameTime * 10);
    }
  }

  /**
   * 碰撞检测系统
   * 检测所有游戏对象之间的碰撞并处理相应逻辑
   *
   * 检测类型:
   * 1. 玩家炮弹 vs 敌机
   * 2. 敌机炮弹 vs 玩家
   * 3. 敌机 vs 玩家直接碰撞
   * 4. 游戏结束条件检查
   */
  private checkCollisions(): void {
    // 1. 检查玩家炮弹与敌机的碰撞
    // 使用倒序遍历避免数组索引问题
    for (let pIndex = this.player.getProjectiles().length - 1; pIndex >= 0; pIndex--) {
      const projectile = this.player.getProjectiles()[pIndex];

      for (let eIndex = this.enemies.length - 1; eIndex >= 0; eIndex--) {
        const enemy = this.enemies[eIndex];

        if (projectile.checkCollision(enemy.mesh)) {
          // 根据炮弹类型确定武器类型
          const weaponType = this.getWeaponTypeFromProjectile(projectile);

          // 敌机受到伤害，传递武器类型用于击毁效果
          enemy.takeDamage(projectile.damage, weaponType);

          // 从场景中移除炮弹
          this.gameWorld.removeFromScene(projectile.mesh);
          this.player.removeProjectile(projectile);

          // 根据武器类型创建不同的爆炸效果
          if (weaponType === 'missile') {
            this.createExplosion(projectile.position, true); // 大爆炸
          } else {
            this.createExplosion(projectile.position); // 普通爆炸
          }

          // 如果敌机被击毁，创建额外的击毁效果
          if (!enemy.isAlive()) {
            this.createEnemyDestroyEffect(enemy.position, weaponType);
          }

          break; // 炮弹击中目标后停止检测
        }
      }
    }

    // 2. 检查敌机炮弹与玩家的碰撞
    this.enemies.forEach(enemy => {
      for (let pIndex = enemy.getProjectiles().length - 1; pIndex >= 0; pIndex--) {
        const projectile = enemy.getProjectiles()[pIndex];

        if (projectile.checkCollision(this.player.mesh)) {
          // 玩家受到伤害
          this.player.takeDamage(projectile.damage);

          // 从场景中移除炮弹
          this.gameWorld.removeFromScene(projectile.mesh);
          enemy.removeProjectile(projectile);

          // 创建命中爆炸效果
          this.createExplosion(projectile.position);
        }
      }
    });

    // 3. 检查敌机与玩家的直接碰撞
    this.enemies.forEach((enemy) => {
      const distance = enemy.position.distanceTo(this.player.position);
      if (distance < 8) { // 增加碰撞检测范围，提高游戏体验
        // 双方都受到碰撞伤害
        this.player.takeDamage(50);
        enemy.takeDamage(100);

        // 创建大型爆炸效果
        this.createExplosion(enemy.position, true);

        // 添加屏幕震动效果
        this.createScreenShake();
      }
    });

    // 4. 检查游戏结束条件
    if (!this.player.isAlive()) {
      this.gameOver();
    }
  }

  /**
   * 创建屏幕震动效果
   * 在发生剧烈碰撞时增强视觉反馈
   */
  private createScreenShake(): void {
    const camera = this.gameWorld.camera;
    const originalPosition = camera.position.clone();

    // 震动动画
    let shakeTime = 0;
    const shakeDuration = 0.5; // 震动持续时间(秒)
    const shakeIntensity = 2;  // 震动强度

    const shakeAnimation = () => {
      if (shakeTime < shakeDuration) {
        // 随机震动偏移
        const shakeX = (Math.random() - 0.5) * shakeIntensity;
        const shakeY = (Math.random() - 0.5) * shakeIntensity;

        camera.position.x = originalPosition.x + shakeX;
        camera.position.y = originalPosition.y + shakeY;

        shakeTime += 0.016; // 约60FPS
        requestAnimationFrame(shakeAnimation);
      } else {
        // 恢复原始位置
        camera.position.copy(originalPosition);
      }
    };

    shakeAnimation();
  }

  private cleanup(): void {
    // 清理玩家炮弹
    this.player.getProjectiles().forEach((projectile) => {
      if (projectile.isOutOfBounds()) {
        this.gameWorld.removeFromScene(projectile.mesh);
        this.player.removeProjectile(projectile);
      } else {
        // 确保炮弹在场景中
        if (!this.gameWorld.scene.children.includes(projectile.mesh)) {
          this.gameWorld.addToScene(projectile.mesh);
        }
      }
    });

    // 清理敌机炮弹
    this.enemies.forEach(enemy => {
      enemy.getProjectiles().forEach((projectile) => {
        if (projectile.isOutOfBounds()) {
          this.gameWorld.removeFromScene(projectile.mesh);
          enemy.removeProjectile(projectile);
        } else {
          // 确保炮弹在场景中
          if (!this.gameWorld.scene.children.includes(projectile.mesh)) {
            this.gameWorld.addToScene(projectile.mesh);
          }
        }
      });
    });
  }

  private updateUI(): void {
    this.scoreElement.textContent = `得分: ${this.score}`;
    this.healthElement.textContent = `生命值: ${Math.round(this.player.health)}`;
    this.energyElement.textContent = `能量: ${Math.round(this.player.energy)}`;

    // 根据生命值改变颜色
    if (this.player.health > 60) {
      this.healthElement.style.color = '#00ff00';
    } else if (this.player.health > 30) {
      this.healthElement.style.color = '#ffff00';
    } else {
      this.healthElement.style.color = '#ff0000';
    }

    // 更新技能UI
    this.updateSkillsUI();
  }

  private updateSkillsUI(): void {
    const skillNames = ['rapidFire', 'laserBeam', 'missile', 'shield'];

    skillNames.forEach(skillName => {
      const element = this.skillElements[skillName];
      if (!element) return;

      const canUse = this.player.canUseSkill(skillName);
      const cooldown = this.player.getSkillCooldown(skillName);
      const skill = this.player.skills[skillName];

      // 更新技能状态样式
      element.className = 'skill-slot';
      if (canUse) {
        element.classList.add('available');
      } else if (cooldown > 0) {
        element.classList.add('cooldown');
      }

      // 更新冷却时间条
      const cooldownBar = element.querySelector('.skill-cooldown') as HTMLElement;
      if (cooldownBar && skill) {
        const cooldownPercent = cooldown / skill.cooldown;
        cooldownBar.style.transform = `scaleX(${cooldownPercent})`;
        cooldownBar.style.display = cooldown > 0 ? 'block' : 'none';
      }
    });
  }

  private createExplosion(position: THREE.Vector3, large: boolean = false): void {
    // 创建简单的爆炸效果
    const particleCount = large ? 20 : 10;
    const particles: THREE.Mesh[] = [];

    for (let i = 0; i < particleCount; i++) {
      const size = large ? 0.5 : 0.2;
      const particleGeometry = new THREE.SphereGeometry(size, 4, 4);
      const particleMaterial = new THREE.MeshBasicMaterial({
        color: large ? 0xff4400 : 0xffaa00,
        transparent: true,
        opacity: 0.8
      });

      const particle = new THREE.Mesh(particleGeometry, particleMaterial);
      particle.position.copy(position);

      // 随机方向和速度
      const direction = new THREE.Vector3(
        (Math.random() - 0.5) * 2,
        (Math.random() - 0.5) * 2,
        (Math.random() - 0.5) * 2
      ).normalize();

      const speed = Math.random() * (large ? 20 : 10) + 5;
      particle.userData = {
        velocity: direction.multiplyScalar(speed),
        life: 1.0,
        maxLife: large ? 2.0 : 1.0
      };

      particles.push(particle);
      this.gameWorld.addToScene(particle);
    }

    // 动画粒子
    const animateParticles = () => {
      particles.forEach((particle, index) => {
        if (particle.userData.life <= 0) {
          this.gameWorld.removeFromScene(particle);
          particles.splice(index, 1);
          return;
        }

        // 更新位置
        particle.position.add(particle.userData.velocity.clone().multiplyScalar(0.016));

        // 更新生命值和透明度
        particle.userData.life -= 0.016;
        const material = particle.material as THREE.MeshBasicMaterial;
        material.opacity = particle.userData.life / particle.userData.maxLife;

        // 减速
        particle.userData.velocity.multiplyScalar(0.98);
      });

      if (particles.length > 0) {
        requestAnimationFrame(animateParticles);
      }
    };

    animateParticles();
  }

  private gameOver(): void {
    this.gameRunning = false;

    // 显示游戏结束信息
    const gameOverDiv = document.createElement('div');
    gameOverDiv.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 30px;
      border-radius: 10px;
      text-align: center;
      font-size: 24px;
      z-index: 1000;
    `;

    gameOverDiv.innerHTML = `
      <h2 style="color: #ff4444; margin-bottom: 20px;">游戏结束</h2>
      <p>最终得分: ${this.score}</p>
      <button onclick="location.reload()" style="
        margin-top: 20px;
        padding: 10px 20px;
        font-size: 18px;
        background: #0088ff;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
      ">重新开始</button>
    `;

    document.body.appendChild(gameOverDiv);

    console.log(`游戏结束！最终得分: ${this.score}`);
  }

  /**
   * 根据炮弹特征判断武器类型
   * @param projectile 炮弹对象
   * @returns 武器类型字符串
   */
  private getWeaponTypeFromProjectile(projectile: any): string {
    // 根据炮弹的属性判断武器类型
    if (projectile.damage >= 80) {
      return 'missile'; // 高伤害的是导弹
    } else if (projectile.damage >= 40) {
      return 'laser'; // 中等伤害的是激光
    } else {
      return 'normal'; // 普通炮弹
    }
  }

  /**
   * 创建敌机击毁效果
   * @param position 击毁位置
   * @param weaponType 武器类型
   */
  private createEnemyDestroyEffect(position: THREE.Vector3, weaponType: string): void {
    // 根据武器类型创建不同的击毁效果
    switch (weaponType) {
      case 'missile':
        this.createMissileDestroyEffect(position);
        break;
      case 'laser':
        this.createLaserDestroyEffect(position);
        break;
      default:
        this.createNormalDestroyEffect(position);
        break;
    }
  }

  /**
   * 导弹击毁效果 - 大型爆炸和冲击波
   */
  private createMissileDestroyEffect(position: THREE.Vector3): void {
    // 创建冲击波效果
    const shockwaveGeometry = new THREE.RingGeometry(0, 1, 16);
    const shockwaveMaterial = new THREE.MeshBasicMaterial({
      color: 0xff4400,
      transparent: true,
      opacity: 0.8,
      side: THREE.DoubleSide
    });
    const shockwave = new THREE.Mesh(shockwaveGeometry, shockwaveMaterial);
    shockwave.position.copy(position);
    this.gameWorld.addToScene(shockwave);

    // 冲击波扩散动画
    const animateShockwave = () => {
      shockwave.scale.multiplyScalar(1.1);
      shockwaveMaterial.opacity -= 0.02;

      if (shockwaveMaterial.opacity > 0) {
        requestAnimationFrame(animateShockwave);
      } else {
        this.gameWorld.removeFromScene(shockwave);
      }
    };
    animateShockwave();

    // 创建火球效果
    this.createFireball(position);
  }

  /**
   * 激光击毁效果 - 能量爆发
   */
  private createLaserDestroyEffect(position: THREE.Vector3): void {
    // 创建能量爆发效果
    const energyBurst = new THREE.Mesh(
      new THREE.SphereGeometry(2, 8, 8),
      new THREE.MeshBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.8
      })
    );
    energyBurst.position.copy(position);
    this.gameWorld.addToScene(energyBurst);

    // 能量爆发动画
    const animateEnergyBurst = () => {
      energyBurst.scale.multiplyScalar(1.05);
      const material = energyBurst.material as THREE.MeshBasicMaterial;
      material.opacity -= 0.03;

      if (material.opacity > 0) {
        requestAnimationFrame(animateEnergyBurst);
      } else {
        this.gameWorld.removeFromScene(energyBurst);
      }
    };
    animateEnergyBurst();
  }

  /**
   * 普通击毁效果 - 标准爆炸
   */
  private createNormalDestroyEffect(position: THREE.Vector3): void {
    // 创建碎片效果
    for (let i = 0; i < 6; i++) {
      const fragment = new THREE.Mesh(
        new THREE.BoxGeometry(0.3, 0.3, 0.3),
        new THREE.MeshBasicMaterial({
          color: 0x666666,
          transparent: true,
          opacity: 1
        })
      );

      fragment.position.copy(position);
      const direction = new THREE.Vector3(
        (Math.random() - 0.5) * 2,
        (Math.random() - 0.5) * 2,
        (Math.random() - 0.5) * 2
      ).normalize();

      this.gameWorld.addToScene(fragment);

      // 碎片飞散动画
      const animateFragment = () => {
        fragment.position.add(direction.clone().multiplyScalar(0.3));
        fragment.rotation.x += 0.1;
        fragment.rotation.y += 0.1;
        const material = fragment.material as THREE.MeshBasicMaterial;
        material.opacity -= 0.02;

        if (material.opacity > 0) {
          requestAnimationFrame(animateFragment);
        } else {
          this.gameWorld.removeFromScene(fragment);
        }
      };

      setTimeout(() => animateFragment(), i * 50);
    }
  }

  /**
   * 创建火球效果
   */
  private createFireball(position: THREE.Vector3): void {
    const fireball = new THREE.Mesh(
      new THREE.SphereGeometry(3, 12, 12),
      new THREE.MeshBasicMaterial({
        color: 0xff6600,
        transparent: true,
        opacity: 0.9
      })
    );
    fireball.position.copy(position);
    this.gameWorld.addToScene(fireball);

    // 火球动画
    const animateFireball = () => {
      fireball.scale.multiplyScalar(1.03);
      const material = fireball.material as THREE.MeshBasicMaterial;
      material.opacity -= 0.025;

      // 颜色变化：橙色 -> 红色 -> 黑色
      if (material.opacity > 0.5) {
        material.color.setHex(0xff6600); // 橙色
      } else if (material.opacity > 0.2) {
        material.color.setHex(0xff0000); // 红色
      } else {
        material.color.setHex(0x330000); // 暗红色
      }

      if (material.opacity > 0) {
        requestAnimationFrame(animateFireball);
      } else {
        this.gameWorld.removeFromScene(fireball);
      }
    };
    animateFireball();
  }
}

// 启动游戏
new SpaceBattleGame();
